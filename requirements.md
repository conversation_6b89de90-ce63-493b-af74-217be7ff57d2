# INFS3822 – AI for Business Analytics
## Individual Assignment 2 – Problem statement and proposed solutions

**CRICOS Provider Code 00098G**

---

## Assignment Overview

In this individual assignment you are required to prepare and submit a **Consultancy Report** to discuss the business problem faced by your client, articulate the business objectives, translate the business problem into machine learning problems, and outline the key proposed solutions along with the expected outcomes.

**Please note that this assignment is worth 30% of your overall course mark.**

### Learning Objectives

In this assignment, students are required to clearly define and explain a business problem faced by a company to demonstrate a good understanding of the business context and explore how companies harness AI-driven business analytics to solve business problems. This includes:

- Conducting background research to explain how ML techniques/algorithms have been applied in industry to conduct analytics to solve similar business problems, along with justifications supported by academic references and relevant examples from industry
- Applying design thinking frameworks to frame the business problem and the key proposed solutions
- Demonstrating ability to put AI-driven analytics into practice by applying AI/ML techniques to conduct analysis
- Discussing the rationale behind the selection of the most suitable ML methods for the analysis
- Explaining how these ML algorithms could be applied to address the business problem
- Discussing the challenges they might encounter during the data analysis and how they will perform data preprocessing to overcome these challenges
- Outlining the steps involved in the deployment of the ML algorithms
- Discussing the expected outcomes of the analysis and the benefits for the company

---

## 1. Case Study

The auto industry has consistently been at the forefront of embracing innovative technologies in manufacturing, marketing, and vehicles. Now, artificial intelligence (AI) is propelling the sector to new, unprecedented heights. By integrating AI into auto sales and marketing, dealerships, and OEMs (Original Equipment Manufacturers) are uncovering new opportunities to engage customers and stimulate growth.

**Gumtree** (gumtree.com.au) is one of Australia's leading online marketplaces where businesses and individuals can sell and buy cars, furniture, electronics, and more across Australia. Gumtree connects more than 6 million buyers and sellers, with over 45,000 new listings daily in categories like Home & Garden, Baby & Children, Sport & Fitness, Clothing & Jewellery and Gumtree Jobs. The primary objective of Gumtree is to facilitate successful, safe local trade. Notably, car sales represent a significant area of growth for the platform.

### The Problem

When sellers submit car listings on Gumtree, they are required to provide detailed information about the vehicle, write a description, and decide on a listing price. Often, users struggle to determine the appropriate price and craft an appealing description when submitting car listings. This can lead to inefficiencies and a lack of successful transactions.

### The Solution

To address this issue, Gumtree plans to leverage AI-driven analytics to assist users in setting the right price. This estimation process, known as "**car valuation**", considers multiple factors including, but not limited to:
- Car brand
- Type
- Location
- Engine size
- Transmission
- And more

### Benefits

A car valuation benefits both buyers and sellers:
- **For buyers**: It offers a clear indication of the car's market value, reducing the risk of overpaying
- **For sellers**: It increases the chances of quick sale

This ensures that the pricing is competitive and reflective of the current market conditions, thereby enhancing user satisfaction and driving more successful transactions on the platform.

---

## 2. Requirements

In a hypothetical scenario, you are working as a **Consultant at Gumtree**, and you have been asked to provide guidance on how to leverage AI-driven analytics to automate Gumtree's valuation process to address the business problem. Your objective is to design and suggest an action plan of how to apply ML algorithms to predict more consistent, accurate, and reliable car market value to improve decision-making for both buyers and sellers.

### Dataset

You are provided with a large historical dataset of vehicle listings in the Greater Sydney area collected from gumtree.com.au as of May 2024. The dataset is available on Moodle as an Excel spreadsheet called **gumtree_listings.xlsx** under the Assessment section and the Datasets sub-section.

#### Dataset Variables

The dataset includes the following variables:

| Variable | Description |
|----------|-------------|
| **listing_url** | Link to a listing on gumtree.com.au |
| **listing_title** | The title of the listing |
| **odometer** | The number of km on the odometer of the vehicle |
| **body_type** | The body type of the vehicle including Sedan, SUV, Ute Van/Mini van, Wagon, Hatchback, Convertible, Coupe (2 door), and Other |
| **transmission** | The transmission of the vehicle including Manual and Auto |
| **manufacturer** | The manufacturer of the vehicle including Alfa Romeo, Audi, Bentley, BMW, Chery, Chevrolet, Chrysler, Citroen, Daihatsu, Datsun, Dodge, Ferrari, Fiat, Ford, Great Wall, GWM, Haval, Hino, Holden, Honda, HSV, Hyundai, Infiniti, Isuzu, Iveco, Jaguar, Jeep, Kia, Lamborghini, Land Rover, LDV, Lexus, Mahindra, Maserati, Mazda, Mercedes-AMG, Mercedes-Benz, MG, Mini, Mitsubishi, Mitsubishi Fuso, Nissan, Other, Peugeot, Porsche, Ram, Range Rover, Renault, Rolls-Royce, Saab, Skoda, Ssangyong, Subaru, Suzuki, Tesla, Toyota, Volkswagen, and Volvo |
| **engine_config** | The configuration of the engine in terms of number of cylinders and engine size. For example, "4 cyl 2.0L" is a 2 litre engine size with 4 cylinders |
| **description** | A description of the listing |
| **price** | The price advertised on gumtree.com.au |
| **price_conditions** | The conditions of the advertised price including Drive Away, Auction, Excl. Gov. Charges, and Negotiable |
| **location** | Suburb where the vehicle is located. e.g. Alexandria, NSW |
| **seller_type** | The type of sale including Dealer demo, Dealer new, Dealer used, and Private |
| **featured_image** | Link to the vehicle photo featured on gumtree.com.au |

### 2.1. Expectations

To succeed in Individual Assignment 2, you will need to manage your learning process carefully – including demonstrating agency in performing self-directed learning, critical thinking, conducting research, taking initiative, and more.

---

## 2.2. Deliverable

In this assignment you are required to submit a **Consultancy Report** (in Word format).

Your Consultancy Report (**2000-word limit**) must include the following components:

### 1. Frame the Business Problem statement (20 marks, 400 words)

- Discuss the nature of the business problem faced by the company
- Apply **ONE (1) Design thinking framework** to frame the problem within the context and identify the underlying causes and the critical factors. e.g., Critical Success Factors (CSF) analysis framework, PEST analysis framework
- Clearly define the problem you aim to solve and outline the business objective

### 2. Discuss the key proposed solutions to address the business problem (30 marks, 600 words)

- Explain how ML techniques/algorithms application would contribute to address the business problem
- Apply **ONE (1) design thinking frameworks** to explain how ML methods/algorithms could be applied to solve the business problem faced by the company. e.g., Business Model Canvas (BMC), Opportunity Canvas for the application of ML
- Articulate the opportunities for improvements and how this would benefit the company. Justify and support your arguments with relevant examples and academic references
- Provide a clear visualisation of your project methodology (road map) to outline the different steps you will undertake to apply ML techniques/algorithms to address the business problem

### 3. Translate the business problem into ML problems (30 marks, 600 words)

- **Explain the rationale behind the selection of relevant ML techniques/methods** to conduct the analysis
- **Explain the steps involved in the implementation of the ML algorithms** to conduct the analysis:
  - Discuss potential challenges that you might encounter during the data analysis
  - Explain the data pre-processing steps you will perform and articulate in what ways this would contribute to address these challenges
  - Discuss the relevant features in the dataset needed for the analysis including the target variable and the other key variables required to build the ML algorithms. Explain the approach you may apply to determine the key predictors and the target variable
  - Articulate the requirements for the ML model development (training, testing and evaluation of the ML models)

### 4. Articulate the expected outcomes of the analysis and discuss how this would address the business problem (20 marks, 400 words)

- Discuss the expected outcomes of the application of AI/ML to conduct the analysis and how this would solve the business problem faced by the company

**NB.** Throughout the consultancy report, your arguments should be justified and supported with academic references and relevant examples.

---

## 2.3. Formatting

### Word Limit

Each section of the consultancy report has a word limit, as indicated in 2.2. Deliverable. The distribution of word count proportionally reflects the complexity and significance of each section, totalling a maximum word length of 2,000 words. There is a **(+10%) leeway** in word limits for each section.

**Please note that tables, figures, diagrams and references are excluded from the word count.**

You should be mindful of the marks awarded to each section, as indicated in 2.2. Deliverable, when allocating the number of words spend on each section.

**Please note that material presented in excess of the word limit for each section will not be considered when grading the assignment.**

### Formatting Requirements

The consultancy report should be in '**business report**' style (in Word format) with the following requirements:

- **Arial 12-point font**
- **1.5 spacing**
- **Page numbers on each page**
- **Individual Assignment 2 Cover page included** (provided on Moodle)
- **All required sections included**, as indicated in 1.1. Deliverable
- **At least 5 relevant academic references** to justify and support your arguments in the report

Feel free to make whatever use of tables, figures, and diagrams that you believe appropriate and relevant to support your work. Tables, figures, diagrams and references do not count towards the word limit.

---

## 2.4. Submission

Upload your Consultancy Report document (in Word format) on Moodle.

- You can only upload one report document
- You are advised to keep a copy of your submission

The originality of the submission will be checked using **Turnitin**. Please check the originality report generated by Turnitin during the submission process.

---

## 2.5. Late Lodgement & Extensions

Late assignments (without approved extensions) will attract a penalty of **5% of the available marks per day of lateness** (including weekends and public holidays). The penalty will be deducted from the mark your assignment is awarded. The assessment will not be accepted after **5 days (120 hours)** of the original deadline unless special consideration has been approved.

General information on special consideration for undergraduate and postgraduate courses can be found in the Assessment Implementation Procedure and the Current Students page.

**Please note:**

- **Extensions are only granted in exceptional circumstances.** You will be required to substantiate your application with appropriate documentary evidence such as medical certificates, accident reports etc.
- **You should note that extensions are not usually granted for computer-related problems or heavy workloads** (at either your job or University).
- **Students are expected to manage their time to meet deadlines** and to request extensions as early as possible before the deadline.

---

## 2.6. References

It is expected that your consultancy report will make use of **at least five recent academic journal articles and practitioner articles**.

A list of references must be included at the end of the consultancy report. The reference list should only list documents / websites that are cited in the assignment (references in text).

Your bibliography and in text citations must be formatted as per the requirements of the **Harvard referencing style**.

For information on how to acknowledge your sources and reference correctly, see:
- https://student.unsw.edu.au/referencing
- https://student.unsw.edu.au/harvard-referencing

---

## 2.7. Penalties

Penalties will apply in the following circumstances:

- The assignment contains spelling and grammatical mistakes
- The submission requirements have not been adhered to
- Failure to use the Harvard referencing style
- The assignment is submitted late (5% marks penalty per day of lateness)
- The assignment contains material which is not properly cited in accordance with university policy
- You should also note that plagiarism or other academic misconduct will not be tolerated, and all instances found will be pursued. At a minimum this typically entails the student being award zero (0) marks the plagiarize assignment

---

## 2.8. Proper academic standards

All assignments are subject to the University's guidelines regarding academic misconduct and as such plagiarism is as unacceptable in this group assignment as it is in other assignment. If plagiarism is found in your assignment it will be fully pursed as per the University (see https://student.unsw.edu.au/plagiarism for details).

---

## 2.9. Use of Generative AI Tools (e.g. ChatGPT) in the Assignment

Given that this assignment requires students to demonstrate their critical thinking and creativity, you are **permitted to use generative AI tools to generate initial ideas**. However, you must develop or edit these ideas to such a significant extent that what is submitted is your own work. Only occasional AI-generated words or phrases may form part of your final submission.

It is advisable to keep copies of the initial prompts to show your lecturer in case there is any uncertainty about the submission of your work. If significant portions of the outputs of Generative AI content, such as that from ChatGPT, are included in your submission, it will be regarded as serious academic misconduct and subject to the standard penalties, which may include failing the assignment (FL), suspension, or exclusion.

### Viva Voce

Any student may be called upon to provide a **viva voce** (from the Latin meaning 'living voice') for any assignment. A viva voce is an interview style meeting where you will be asked to explain, discuss, or use information related to any assignment or work produced for this course. These can be used to ascertain knowledge and ability including the extent to which the student has undertaken the required reading, done preparatory work and can demonstrate understanding of what they have written or presented. Viva voces are used in conjunction with submitted assessment work not instead of submitted work.

*This notice is used with permission created by Assoc Prof Lynn Gribble, UNSW Sydney*

---

## 2.10. Copyright

### WARNING

*This material has been reproduced and communicated to you by or on behalf of the University of New South Wales in accordance with section 113P(1) of the Copyright Act 1968 (Act). The material in this communication may be subject to copyright under the Act. Any further reproduction or communication of this material by you may be the subject of copyright protection under the Act.*

*Do not remove this notice*

There are some file-sharing websites that specialise in buying and selling academic work to and from university students.

If you upload your original work to these websites, and if another student downloads and presents it as their own either wholly or partially, you might be found guilty of collusion — even years after graduation.

These file-sharing websites may also accept purchase of course materials, such as copies of lecture slides and tutorial handouts. By law, the copyright on course materials, developed by UNSW staff in the course of their employment, belongs to UNSW. It constitutes copyright infringement, if not academic misconduct, to trade these materials.