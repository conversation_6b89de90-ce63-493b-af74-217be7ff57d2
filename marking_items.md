# Assessment Rubric

## Criteria and Scales

| Criteria | Scale 1 (0.00) | Scale 2 | Scale 3 | Scale 4 | Scale 5 |
|----------|----------------|---------|---------|---------|---------|
| **1-BUS_PROBLEM** | 0.00 | 1.25 | 2.50 | 3.75 | 5.00 |
| Discuss the nature of the business problem faced by the company | No answer / incorrect | Weak | Average | Great | Excellent |
| **1-BUS_THINK** | 0.00 | 0.75 | 1.50 | 2.25 | 3.00 |
| Apply ONE (1) Design thinking framework to frame the business problem | No answer / incorrect | Weak | Average | Great | Excellent |
| **1-CONTEXT** | 0.00 | 0.75 | 1.50 | 2.25 | 3.00 |
| Frame the problem within the relevant context | No answer / incorrect | Weak | Average | Great | Excellent 
| **1-FACTORS** | 0.00 | 1.00 | 2.00 | 3.00 | 4.00 |
| Identify the underlying causes and the critical factors | No answer / incorrect | Weak | Average | Great | Excellent |
| **1-BUS_OBJ** | 0.00 | 1.25 | 2.50 | 3.75 | 5.00 |
| Clearly define the problem you aim to solve and outline the business objective | No answer / incorrect | Weak | Average | Great | Excellent |
| **2-ML_SOLUTION** | 0.00 | 1.25 | 2.50 | 3.75 | 5.00 |
| Explain how ML techniques/algorithms application would contribute to address the business problem | No answer / incorrect | Weak | Average | Great | Excellent |
| **2-ML_THINK** | 0.00 | 2.50 | 5.00 | 7.50 | 10.00 |
| Apply ONE (1) Design thinking framework to explain how ML can be applied | No answer / incorrect | Weak | Average | Great | Excellent |
| **2-OPPORTUN** | 0.00 | 1.25 | 2.50 | 3.75 | 5.00 |
| Articulate the opportunities for improvements | No answer / incorrect | Weak | Average | Great | Excellent |
| **2-METHODOLOGY** | 0.00 | 2.50 | 5.00 | 7.50 | 10.00 |
| Provide a clear visualisation of your project methodology (road map) | No answer / incorrect | Weak | Average | Great | Excellent |
| **3-MLRATIONALE** | 0.00 | 1.25 | 2.50 | 3.75 | 5.00 |
| Explain the rationale behind the selection of relevant ML techniques/methods | No answer / incorrect | Weak | Average | Great | Excellent |
| **3-CHALLENGES** | 0.00 | 1.25 | 2.50 | 3.75 | 5.00 |
| Discuss potential challenges that you might encounter during the data analysis | No answer / incorrect | Weak | Average | Great | Excellent |
| **3-STEP_PREP** | 0.00 | 1.00 | 2.00 | 3.00 | 4.00 |
| Explain the data pre-processing steps you will perform and articulate in what ways this would contribute to address these challenges | No answer / incorrect | Weak | Average | Great | Excellent |
| **3-STEP_FEAT** | 0.00 | 1.00 | 2.00 | 3.00 | 4.00 |
| Discuss the relevant features in the dataset needed for the analysis including the target variable and the other key variables required to build the ML algorithms. Explain the | No answer / incorrect | Weak | Average | Great | Excellent |
| **3-STEP_TRAIN** | 0.00 | 1.00 | 2.00 | 3.00 | 4.00 |
| Articulate the requirements for the ML model development – Training | No answer / incorrect | Weak | Average | Great | Excellent |
| **3-STEP_TEST** | 0.00 | 1.00 | 2.00 | 3.00 | 4.00 |
| Articulate the requirements for the ML model development – Testing | No answer / incorrect | Weak | Average | Great | Excellent |
| **3-STEP_EVAL** | 0.00 | 1.00 | 2.00 | 3.00 | 4.00 |
| Articulate the requirements for the ML model development – Evaluation | No answer / incorrect | Weak | Average | Great | Excellent |
| **2-OUTCOMES** | 0.00 | 2.50 | 5.00 | 7.50 | 10.00 |
| Discuss the expected outcomes of the application of AI/ML to conduct the analysis | No answer / incorrect | Weak | Average | Great | Excellent |
| **2-PROB_SOLVE** | 0.00 | 2.50 | 5.00 | 7.50 | 10.00 |
| Discuss how the outcomes of the application of AI/ML would solve the business problem faced by the company | No answer / incorrect | Weak | Average | Great | Excellent |