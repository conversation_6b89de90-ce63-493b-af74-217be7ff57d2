# INFS3822 – AI for Business Analytics
## CONSULTANCY REPORT / ASSIGNMENT 2
### Term 2, 2025

---

## Cover page for Individual Assignment 2

### SUBMISSION DETAILS
**Student Number:** [Box for entry]  
**Student Name:** [Box for entry]

### DECLARATION
I declare that this assessment item is my own work, except where acknowledged, and has not been submitted for academic credit elsewhere, and acknowledge that the assessor of this item may, for the purpose of assessing this item:

• Reproduce this assessment item and provide a copy to another member of the University; and/or,  
• Communicate a copy of this assessment item to a plagiarism checking service (which may then retain a copy of the assessment item on its database for the purpose of future plagiarism checking).

By submitting this assessment, I certify that I have read and understood the University Rules in respect of Student Academic Misconduct.

### INSTRUCTIONS
Proceed to next page for your Individual Assignment 1 Analytics Report.

Make sure you follow the file naming instructions to save and name this file before you submit this file via course Moodle Individual Assessment 2 Submission Link.

Make sure you submit your assignment with this cover sheet. Failure to do so will result in 10% penalty of the marks available for this assignment.

---

## CONTENTS

- Executive Summary - 4
- Introduction - 6
- Frame the Business Problem statement - 7
  - Discuss the Nature of the Business Problem Faced by the Company - 7
  - Business Problem - 7
  - Business Objective - 7
  - Problem Statement - 7
- Design Thinking Framework - 9
  - Environmental Factors - 10
  - Industry-Related Factors - 10
  - Peer-Related Factors - 10
  - Temporal Factors - 11
  - Management-Position Factors - 11
- Discuss the key proposed solutions to address the business problem - 12
  - How Machine Learning Technique/Algorithms can address the Business Problem - 12
  - Overview - 12
  - Design Thinking Framework: Business Model Canvas - 14
  - Opportunities for improvements and benefits to Gumtree - 15
  - Enhance Accuracy in Car Valuations - 15
  - Improved User Experience - 16
  - Continuous Model Improvement - 16
- Project Methodology - 17
  - Overall Roadmap - 17
  - Translate the business problem into ML problems - 18
  - Rationale of selection of relevant ML techniques/methods - 18
  - Comparison of Models - 19
  - Three Models to choose from - 20
  - Selection of the Best ML technique - 21
  - Implementation of ML Algorithms - 22
  - Potential Challenges During Data Analysis - 22
  - Step 1: Data Collection and Integration - 23
  - Step 2: Data Preprocessing Steps - 23
  - Step 3: Model Selection and Training - 26
  - Step 4: Requirements for ML Model Development - 26
- Articulate the expected outcomes of the analysis and discuss how this would address the business problem - 30
- Conclusion - 32
- References - 33
- Appendix - 38
  - Pest Analysis Framework - 38
  - Challenge 1: Incomplete/Missing Data - 39

---

## EXECUTIVE SUMMARY

Gumtree, a prominent Australian online marketplace, is experiencing challenges in its car sales segment due to inaccurate pricing and unappealing descriptions, which has led to reduced transaction success. This report aims to address these issues by leveraging AI-driven analytics to automate the car valuation process, enhancing user satisfaction and increasing transaction success rates.

### PROBLEM STATEMENT AND BUSINESS OBJECTIVE

Gumtree's manual pricing process is prone to errors, leading to incorrect pricing that affects sales and profits. Buyers face uncertainty and reduced trust in the platform due to inaccurate pricing, hindering successful trades and overall user satisfaction. The primary objective is to leverage AI-driven analytics to provide accurate and competitive car valuations, ensuring a seamless user experience and maintaining data accuracy, thereby increasing user satisfaction and transaction success rates.

### DESIGN THINKING FRAMEWORK: CRITICAL SUCCESS FACTOR ANALYSIS

This report employs the Critical Success Factor (CSF) analysis framework to identify key factors influencing Gumtree's success in the car sales segment. Environmental factors such as economic trends and technological advancements in AI, industry-related factors like the implementation of advanced machine learning models, and peer-related factors focusing on unique features and security measures are examined. Additionally, temporal factors such as agile responses to market conditions and management-position factors emphasising operational efficiency and continuous improvement are considered.

### PROPOSED SOLUTIONS

The report proposes the application of various machine learning techniques, including regression analysis, decision trees, random forests, and neural networks, to automate the car valuation process. These algorithms will analyse historical sales data, market trends, and car attributes to provide accurate pricing suggestions. This approach will enhance pricing accuracy, streamline the listing process, and continuously adapt to market trends, building trust and improving operational efficiency.

### IMPLEMENTATION STEPS

The implementation involves data collection and integration, data preprocessing, model selection and training, and continuous monitoring and improvement. Key features such as brand, model, mileage, age, location, engine size, transmission type, body type, seller type, days listed, and seller reputation are identified for building the machine learning models. The data will be split into training, validation, and testing sets to ensure robust model performance.

### EXPECTED OUTCOMES

The expected outcomes include enhanced pricing accuracy, improved user experience, continuous adaptation to market trends, building trust and credibility, and increased operational efficiency. These outcomes will address Gumtree's business problem by providing precise car valuations, reducing errors, and enhancing user satisfaction and transaction success rates.

---

## INTRODUCTION

Gumtree, one of Australia's leading online marketplaces, faces significant challenges in its car sales segment due to inaccuracies in car pricing and unappealing descriptions provided by users. This issue not only affects sales and profits but also diminishes buyer trust and satisfaction, leading to reduced transaction success rates. Despite the Australian auto industry being valued at $173.5 billion (IBISWorld, 2023), Gumtree's inefficient manual pricing process results in pricing errors and user dissatisfaction, which hinders successful trades.

---

## FRAME THE BUSINESS PROBLEM STATEMENT

### DISCUSS THE NATURE OF THE BUSINESS PROBLEM FACED BY THE COMPANY

#### BUSINESS PROBLEM
Gumtree, a leading Australian online marketplace, struggles with car sales due to inaccurate pricing and unappealing descriptions, reducing transaction success. Gumtree's manual pricing process leads to errors, user dissatisfaction, and decreased trust, impacting sales and overall user experience.

#### BUSINESS OBJECTIVE
To enhance user satisfaction and increase transaction success rates on Gumtree by utilising advanced analytics for accurate and competitive pricing, aligning with its mission to provide a trusted and efficient online marketplace for buyers and sellers (Gumtree, 2024).

#### PROBLEM STATEMENT
How Might We leverage AI-driven analytics on Gumtree's car listing platform to provide accurate and competitive car valuations, ensure a seamless user experience, and maintain data accuracy, thereby increasing user satisfaction and transaction success rates?

**Note:** For this business problem, using descriptions and NLP is redundant, as our problem statement indicates it is neither the root cause of the issue nor effective for machine learning applications.

### Figure 1: Applying the 5W1H Approach to Frame the Problem

**01 What**  
Users struggle to set appropriate vehicle prices and write appealing descriptions on Gumtree.

**02 Who**  
Sellers, buyers, and Gumtree are all affected.

**03 Why**  
The manual pricing and description process is inefficient and error-prone.

**04 How**  
Implement AI-driven analytics for accurate pricing and NLP for compelling descriptions.

**05 Where**  
The issue impacts Gumtree's car sales segment the most.

---

## DESIGN THINKING FRAMEWORK

### TABLE 1: CRITICAL SUCCESS FACTOR ANALYSIS FOR GUMTREE

| Critical Success Factor Theme | Context | Critical Factors |
|-------------------------------|---------|------------------|
| Environmental Factors | External elements like economic conditions, technology advancements, and regulations impacting Gumtree's AI-driven car valuation. | Economic trends, AI advancements, regulatory compliance. |
| Industry-Related Factors | Necessary tasks for Gumtree to stay competitive in online car sales. | Advanced ML models, seamless user experience, data accuracy. |
| Peer-Related Factors | Gumtree's position relative to competitors. | Unique features, security measures, algorithm accuracy. |
| Temporal Factors | Short-term factors temporarily affecting Gumtree's operations. | Agile response, quick model updates, consumer behaviour shifts. |
| Management-Position Factors | Internally identified factors focusing on operational efficiency, continuous improvement, and employee engagement. | AI tool refinement, stakeholder engagement, strategic alignment. |

*Ideas sourced from: (CMOE, 2024), (Alqahtani and Rajkhan, 2020), (Julianelli et al., 2020)*

For initial analysis, refer to the "Appendix Table A: PEST Analysis of Gumtree and the Auto Industry" in the appendix section.

### ENVIRONMENTAL FACTORS

Economic trends, such as downturns, can decrease demand for cars, affecting transaction volumes on Gumtree (Appendix Table A). Technological advancements in AI and machine learning offer significant opportunities to enhance the accuracy and efficiency of car valuations by assessing various factors that may influence prices (CWRU Online Engineering, 2024). For example, Tesla has adapted its strategy in response to stock price fluctuations and increased demand for used vehicles, implementing machine learning models to predict second-hand vehicle prices more accurately (Erfan and Dhaka, 2021). This demonstrates the importance of leveraging advanced technologies and compliance to maintain market relevance and user trust.

### INDUSTRY-RELATED FACTORS

Regarding industry-related factors, Gumtree must implement advanced machine learning models to provide accurate pricing information, especially in comparison to competitors. Accurate car valuations attract and retain users by offering reliable pricing information which in turn ensures a seamless user experience. Maintaining data accuracy from listings is also crucial for the platform's credibility. CarGurus, for instance, uses its Instant Market Value (IMV) tool to provide competitive pricing by analysing market trends and historical sales data, positioning itself as a leader in transparent pricing (Borah and Rutz, 2024).

### PEER-RELATED FACTORS

Gumtree must offer unique features like personalized pricing suggestions and enhanced security measures. Continuous improvement in algorithm accuracy and user satisfaction is crucial for maintaining a competitive edge. Carsales provides various research tools and accurate pricing data, helping build consumer trust. Incorporating similar features will enhance Gumtree's market position and attract more users (carsales, 2024).

### TEMPORAL FACTORS

Agile responses to market conditions, such as economic downturns or sudden shifts in consumer behaviour, are essential for Gumtree. During the COVID-19 pandemic, many automotive platforms, like Edmunds, adapted their operations to accommodate increased online activity and reduced in-person interactions (Rogers, 2020), (Sonsale and Phadtare, 2022). Similarly, Gumtree must quickly update its valuation models to reflect current market conditions and maintain relevance and user satisfaction.

### MANAGEMENT-POSITION FACTORS

Management-position factors emphasize operational efficiency, continuous improvement, and employee engagement. Gumtree should refine its AI valuation tool and engage employees to align with company goals. Tesla's ongoing Autopilot updates demonstrate the importance of management-led initiatives for maintaining technological leadership and operational efficiency (Laurell and Sandström, 2021). Similarly, Gumtree's operations and strategic alignment could utilise a similar approach.

### Figure 2: Critical Success Factors for Gumtree

**Theme 1: Enhance AI-Driven Car Valuation**
- Enhance Market Competitiveness
- Increase Valuation Accuracy
- Provide Accurate Car Valuations
- Incorporate Market Data
- Use Advanced ML Models
- Regular Model Updates
- Integrate multiple data sources
- Use real-time data feeds

**Theme 2: Increase Operational Efficiency**
- Optimise Processes
- Enhance Data Quality
- Improve Data Processing
- Ensure data accuracy
- Clean and Pre-Process Data
- Automate routine tasks
- Integrate AI in workflow and streamline operations

---

## DISCUSS THE KEY PROPOSED SOLUTIONS TO ADDRESS THE BUSINESS PROBLEM

### HOW MACHINE LEARNING TECHNIQUE/ALGORITHMS CAN ADDRESS THE BUSINESS PROBLEM

#### OVERVIEW

Applying machine learning techniques, including regression analysis, decision trees, and neural networks, is crucial for addressing Gumtree's inaccurate car valuations and poor user satisfaction. These advanced algorithms automate the valuation process, ensuring precise pricing, reducing overpricing or under-pricing, and increasing transaction success rates and user satisfaction (Cong et al., 2022).

### TABLE 2: POTENTIAL ML TECHNIQUES/ALGORITHMS TO GUMTREE'S BUSINESS PROBLEM

| ML Technique/Algorithm | How it would help Gumtree solve the Business Problem |
|------------------------|------------------------------------------------------|
| Regression Analysis | Identifies relationships between car features (e.g., manufacturer, model, kilometres) and their impact on market price. Suitable for predicting continuous values, providing a clear understanding of how each feature affects the car's value. |
| Decision Trees | Handles mixed data types and captures non-linear relationships. Provides clear decision rules that can consider various factors such as location and seasonal trends, helping in more accurate car valuations. |
| Random Forests | Improves prediction accuracy by aggregating the results of multiple decision trees. Reduces overfitting and handles missing data, offering robust and reliable car valuations. |
| Neural Networks | Captures complex, non-linear patterns in data. Suitable for large datasets, providing highly accurate price predictions by learning intricate relationships between multiple car attributes like engine size, body type, and transmission type. |
| K-Means Clustering | Segments the market into different categories based on car features such as manufacturer, body type, and transmission. Helps tailor pricing strategies to different market segments, offering insights into optimal pricing for various types of cars. |
| Gradient Boosting Machines | Combines multiple weak prediction models to form a strong predictor. Enhances accuracy and performance of car valuation models by focusing on errors of previous models and reducing them iteratively. |
| Support Vector Machines (SVM) | Can be used to predict car prices with high accuracy by finding the best-fit that represents the relationship between variables and price. |

*Ideas sourced from: (Alquthami et al., 2022), (Kashyap, 2023)*

### DESIGN THINKING FRAMEWORK: BUSINESS MODEL CANVAS

The Business Opportunity Canvas (BOC) in Figure 3 provides a structured approach to identify, analyse, and implement AI-driven solutions to enhance Gumtree's car valuation process, thereby improving user satisfaction and transaction success rates.

### Figure 3: Opportunity Canvas Applied to AI-driven Business Analytics

**Business problem**  
How Might We leverage AI-driven analytics on Gumtree's car listing platform to provide accurate and competitive car valuations, ensure quick sales.

**Key stakeholders/Partners**  
- Sellers: Need accurate car valuations to provide reasonable descriptions to ensure quick sales
- Buyers: Require realistic pricing information to make informed purchasing decisions
- Gumtree Management: Aims to enhance the platform's user experience, increase user satisfaction, and gain competitive edge.

**Key Proposed Solution**  
To solve Gumtree's business problem, enhance accuracy and usefulness of car valuations. Methods include regression analysis and decision trees based on factors including manufacturer, mileage, transmission, manufacturer, engine configuration, price conditions, location, and seller type to build a robust model, particularly decision trees and random forests for handling non-linear relationships and neural networks as additional machine learning patterns. Decision trees and random forests are suitable for their robustness while neural networks are a focus on performance metrics. The accuracy is implemented to...

**How to implement the solution**  
To implement the ML solution, we need data from external sources such as demographics, manufacturer, engine configuration, price conditions, location, and seller type. These variables ensure accurate predictions. The implementation will involve data cleaning, gathering, normalising. The analysis will involve splitting the data into training and testing using cross-validation with training methods well include regression analysis, decision trees, and neural networks. The model will be deployed through Gumtree's websites and mobile platforms, integrated into the existing system.

**Challenges**  
Challenge 1: Ensuring Data Quality and Consistency
Implementation of various data cleaning and normalization techniques to handle missing values, removing outliers, and normalising data to ensure consistency

Challenge 2: Handling Market Fluctuations
Continuously update the ML models with the latest market data and trends.

**Unique Value Proposition**  
Outcome 1: Enhanced accuracy and reliability of car valuations
Opportunity 1: Continuous improvement of the ML solution, Leverage data from existing user feedback
Benefit 1: Increased user satisfaction and trust, leading to higher transaction volumes
Benefit 2: Competitive advantage in the online car marketplace through superior technology and service

**Key metrics to assess the performance of the ML models**  
How will the model's performance measure it continues to perform well over time?
1) Regularly evaluate model performance using metrics such as Mean Absolute Error (MAE), Root Mean Squared Error (RMSE) for regression models
2) Collect user feedback to assess the accuracy and usefulness of the predicted valuations in real-world scenarios

How would you assess the performance of the ML models to make predictions?
Use statistical validation techniques to ensure robust evaluation of the model performance

---

## OPPORTUNITIES FOR IMPROVEMENTS AND BENEFITS TO GUMTREE

### TABLE 3: OPPORTUNITIES FOR IMPROVEMENTS AND BENEFITS TO GUMTREE

| Opportunity for Improvement | Benefits for Gumtree | Benefits for Customers |
|----------------------------|---------------------|------------------------|
| Enhanced Accuracy in Car Valuations | Increased accuracy in car valuations will lead to higher transaction success rates and reduced time for listings to be sold. | Buyers and sellers will experience more transparent and reliable pricing, enhancing trust and satisfaction. |
| Improved User Experience | Greater user engagement and higher platform activity will drive increased revenue and market share. | Simplified listing processes and accurate pricing will improve the overall user experience. |
| Continuous Model Improvement | Staying ahead of market trends ensures Gumtree remains competitive and can adapt quickly to changes in the market. | Continuous updates to valuation models ensure users receive the most accurate and current pricing information. |

*Sourced from: (Albert et al., 2013), (Grewal et al., 2004), (Peng et al., 2023)*

### ENHANCE ACCURACY IN CAR VALUATIONS

By using advanced ML techniques like regression analysis and random forests, Gumtree can achieve more precise car valuations. These techniques analyse historical sales data, market trends, and various car features such as odometer readings, body type, transmission, manufacturer, engine configuration, and location to predict accurate prices. Accurate valuations lead to quicker sales as listings are competitively priced, reducing the time cars spend on the platform and increasing transaction volume and efficiency. Enhanced accuracy also strengthens Gumtree's reputation as a reliable marketplace, enhancing trust as evident in Table 3. Buyers can trust the prices listed, reducing the risk of overpaying, and sellers can price their cars competitively, increasing the likelihood of a quick sale.

This can be seen through Zillow, which uses advanced ML algorithms to provide accurate real estate valuations. By leveraging extensive datasets on property attributes and market trends, Zillow offers precise pricing, revolutionizing the real estate market, building trust, and enhancing user satisfaction (Muralidharan et al., 2018). This was evident in a study on the Boston housing market that demonstrated the effectiveness of ML techniques, indicating potential benefits for Gumtree's car valuations.

### IMPROVED USER EXPERIENCE

Automating the car valuation process with ML techniques simplifies the listing process for sellers. It provides immediate, data-driven price suggestions, making it easier for users to list their cars. A seamless and user-friendly listing process encourages more users to list their cars on Gumtree, increasing the number of active listings and transactions. Higher platform activity leads to greater revenue and market share, as noted in Table 3. Sellers benefit from a hassle-free listing experience, while buyers enjoy more accurate and fair pricing. This improved experience encourages repeat use and fosters loyalty to the platform, enhancing trust and satisfaction.

This can be seen through Netflix, which uses ML algorithms to personalize user recommendations (Steck et al., 2021). By leveraging extensive datasets on user preferences and viewing history, Netflix offers tailored content suggestions, improving user satisfaction and engagement. Similarly, by applying ML techniques, Gumtree can enhance user experience, streamline the listing process, and foster loyalty, leading to increased platform activity and market share.

### CONTINUOUS MODEL IMPROVEMENT

Machine learning models, particularly neural networks, can continuously learn from new data, ensuring the car valuation model stays current with market trends and user behaviours. This adaptive capability allows Gumtree to maintain competitive, accurate valuations, quickly adapting to market fluctuations, and providing consistent value to users. As a result, users receive the most accurate pricing information, enhancing their trust in the platform, as evident in Table 3.

This is like Amazon's approach, where continuous model improvement through ML algorithms optimizes product recommendations and pricing strategies. Amazon's dynamic adaptation to user behaviour and market trends has significantly enhanced customer satisfaction and trust (Valdez-Juárez et al., 2021). Similarly, by employing continuous ML model improvement, Gumtree can ensure up-to-date valuations, fostering user trust and maintaining its competitive edge.

---

## PROJECT METHODOLOGY

### OVERALL ROADMAP

**Note:** these steps will be utilised directly in next sections.

### Figure 5: Project Roadmap

**01 Data Preprocessing**
- Prepare the data by cleaning, normalising missing values, handling outliers and missing information and performing feature engineering. Split the dataset into training and testing sets.

**02 Data Collection and Integration**
- Gather relevant data from Gumtree's car listings and external sources to build the ML models. Ensure data privacy and compliance with regulations.

**03 Model Evaluation and Validation**
- Assess the performance of the trained models using metrics such as MAE, RMSE, and R-squared to validate the selected model with the training dataset.

**04 Deployment and Integration**
- Implement the selected ML model into Gumtree's web and mobile platforms using APIs. Integrate the model into the existing car listing workflow for realtime pricing suggestions.

**05 Continuous Monitoring and Improvement**
- Monitor model performance, collect user feedback, and continuously update the model to adapt to changing market conditions and user behaviour.

**06 Model Selection and Training**
- Select appropriate ML algorithms (regression, decision trees, random forests, neural networks). Train these models using the training dataset with cross-validation to tune hyperparameters.

---

## TRANSLATE THE BUSINESS PROBLEM INTO ML PROBLEMS

### RATIONALE OF SELECTION OF RELEVANT ML TECHNIQUES/METHODS

### TABLE 4: MACHINE LEARNING ALGORITHMS SUITED FOR PREDICTING CAR VALUATION

| Criteria | Regression (Supervised) | Classification (Supervised) | Ranking (Supervised) | Clustering (Unsupervised) | Anomaly Detection (Unsupervised) |
|----------|------------------------|----------------------------|---------------------|---------------------------|----------------------------------|
| Predict accurate car valuations | Yes: good for linear relationships | No: not suitable for continuous data | No: not designed for this task (valuations) | No: groups data, not suitable for valuations | No: detects anomalies, not valuations. |
| Identify Factors influencing Price | Yes: can identify linear relationships | Yes: good for mixed data types | No: not used for this business problem | No: not for this purpose | No: not for this purpose |
| Reflects current market conditions | Yes: updates with new data | Yes: can adapt to market changes | No: ranks based on fixed criteria | No: clusters based on static groups | No: focuses on outliers, not new trends. |
| Explain Model Predictions | Yes: provides linear interpretability | Yes: provides clear decision rules | No: not used for explanations | No: not for this purpose | No: not helpful in terms of interpretability |

### COMPARISON OF MODELS

### TABLE 5: COMPARISON OF KEY MODELS FOR ANALYSIS

| Model Type | Suitability for Car Valuation | Limitations | Key benefits |
|-----------|-------------------------------|-------------|--------------|
| Regression (Linear) | Suitable for predicting car value based on linear relationships | Assumes linearity | Easy to implement & interpret |
| Classification (Logistic Regression) | Not suitable for continuous pricing data | Assumes linearity between variables | Not ideal for pricing (non-binary) |
| Classification (Decision Tree) | Ideal for predicting car values with mixed and categorical data | Small changes in data can result in large fluctuation in tree | Handles non-linear relationships & has clear decision rules |
| Classification (Random Forest) | Suitable for complex relationships with multiple features | Requires more computational power | Improves accuracy by reducing risk of overfitting and can also handles missing data |
| Classification (Neural Network) | Suitable for predicting complex patterns in car valuation data | Requires computational power and is less interpretable | Can help identify complex patterns/relationships |
| Ranking | Not suitable for predicting car prices | Ordering/prioritising benefit – other then that not much else | - |
| Clustering | Not suitable for predicting car prices | Groups data points | - |
| Anomaly Detection | Not suitable for car valuation | Identifies outliers and unusual patterns | - |

### THREE MODELS TO CHOOSE FROM

### TABLE 6: REGRESSION, DECISION, RANDOM FOREST AND NEURAL NETWORKS

| Model Selection |
|-----------------|
| **Regression Analysis** - Suitable for predicting continuous values and identifying linear relationships between car features and their impact on market price. It provides clear interpretability and is ideal for understanding how each feature affects the car's value. |
| **Decision trees and Random Forests** - These techniques handle mixed data types and capture non-linear relationships. They provide clear decision rules and are robust against overfitting, making them suitable for car valuation tasks. |
| **Neural Networks** - Capable of capturing complex, non-linear patterns in large datasets. They provide highly accurate price predictions by learning intricate relationships between multiple car attributes. |

### SELECTION OF THE BEST ML TECHNIQUE

#### DECISION TREES AND RANDOM FORESTS

Decision Trees and Random Forests are ideal for Gumtree's car valuations due to their ability to handle complex, non-linear relationships and provide clear, interpretable decision rules. Decision Trees rank feature importance, aiding in identifying key factors, but can overfit. Random Forests reduce overfitting by aggregating multiple decision trees, offering robust and reliable valuations. This combination ensures a balance of interpretability and accuracy, making them optimal for Gumtree's needs (Ali et al., 2012; Prajwala et al., 2015).

---

## IMPLEMENTATION OF ML ALGORITHMS

**Note:** Our Client Gumtree uses SAS Viya to conduct analytics, therefore we will tailor our implementation using this.

### POTENTIAL CHALLENGES DURING DATA ANALYSIS

#### ENSURING DATA QUALITY AND CONSISTENCY

Incomplete or inconsistent car listing data can lead to inaccurate valuations, as evidenced by the current dataset's missing values (Appendix Figure A). This inconsistency hinders Gumtree's ability to provide reliable car prices, affecting user trust and satisfaction. Addressing these data quality issues is crucial for accurate ML model predictions and improved platform reliability.

#### HANDLING MARKET FLUCTUATIONS

The car market is dynamic, with prices fluctuating due to various factors such as economic trends, seasonal demand, and regional differences (IBISWorld, 2023). This volatility requires Gumtree to continuously update their ML models to maintain accurate car valuations, ensuring the platform remains competitive and trustworthy for users.

### STEP 1: DATA COLLECTION AND INTEGRATION

Gather relevant data from Gumtree's car listings and external sources to build the ML models. Ensure data privacy and compliance with regulations when collecting data.

### STEP 2: DATA PREPROCESSING STEPS

### TABLE 7: DATA PREPROCESSING

| Steps | Description |
|-------|-------------|
| Importing Data | Use SAS Viya to import the car listings data. |
| Cleaning data | Identify and handle missing values using imputation techniques. Remove outliers and ensure data consistency through normalisation. |
| Feature Engineering | Create new features such as 'days listed' and 'seller reputation' to provide additional insights for the models. |
| Data Transformation | Convert categorical variables into dummy variables to enable machine learning algorithms to process them effectively. |

These preprocessing steps ensure Gumtree's data is reliable and consistent, addressing incomplete listings. Creating new features and converting variables make the data suitable for advanced modelling (Çetin et al., 2022). Regularly updating models with fresh data adapts to market changes, ensuring accurate car valuations, enhancing user trust, and improving transaction success rates.

### FEATURE SELECTION APPROACH

| Approach | Description |
|----------|-------------|
| Correlation Analysis | Identify features with high correlation to the target variable (car price) using SAS Viya. |
| Domain Expertise | Include features that are known to impact car valuations based on industry knowledge. |
| Model Performance | Use feature importance metrics from initial model runs in SAS Viya to refine the feature set. |

### RELEVANT FEATURES NEEDED FOR THE ANALYSIS

**Target Variable:** Car price (continuous value)  
**Key Features:** Brand, model, mileage, age, location, engine size, transmission type, body type, seller type, days listed, seller reputation

| Features Selected/Dropped | Reason |
|---------------------------|--------|
| listing_url - Dropped | Unique identifier with no predictive value. |
| listing_title - Dropped | Text description with no direct predictive value for the model. |
| odometer - Selected | Mileage is a key factor in car depreciation and market value. |
| body_type - Selected | Body type impacts market demand and pricing. |
| transmission - Selected | Transmission type affects buyer preferences and car valuation. |
| manufacturer - Selected | Manufacturer influences market price due to brand reputation and value. |
| engine_config - Selected | Engine configuration impacts performance and fuel efficiency, affecting car value. |
| description - Dropped | Textual data which may require complex NLP techniques, not directly useful for initial modelling. |
| price - Selected | Target variable. |
| price_conditions - Selected | Pricing conditions (e.g., Drive Away, Negotiable) can influence buyer decision-making and final price. |
| location - Selected | Location affects car prices due to regional market differences. |
| seller_type - Selected | Different seller types (e.g., Dealer, Private) can impact pricing strategies and buyer trust. |
| featured_image - Dropped | Link to image with no direct predictive value in the current context. |
| days_listed - Selected | Feature engineered to measure how long the car has been listed, which affects buyer urgency and price adjustments. |
| seller_reputation - Selected | Feature engineered to assess the seller's reputation, impacting buyer trust and perceived value. |

The relevant features in Gumtree's dataset, essential for building ML algorithms, include car price (target variable), brand, model, mileage, age, location, engine size, transmission type, body type, seller type, days listed, and seller reputation. These features directly influence car valuations by capturing key attributes that affect market value. Non-essential features like listing URL, title, and image are excluded due to their lack of predictive value (Fic et al., 2022). Feature engineering and data transformation further refine the dataset for optimal modelling performance.

### STEP 3: MODEL SELECTION AND TRAINING

To address Gumtree's inaccurate car valuations, ML algorithms like regression, decision trees, random forests, and neural networks will be employed. Random forests reduce overfitting, while neural networks capture complex patterns. Training on Gumtree's data with cross-validation ensures optimal performance, enhancing valuation accuracy, user satisfaction, and trust.

### STEP 4: REQUIREMENTS FOR ML MODEL DEVELOPMENT

### TABLE 8: REQUIREMENTS FOR ML MODEL DEVELOPMENT

| Process | Description | Justification |
|---------|-------------|---------------|
| **Training** | Use a diverse and representative training set to capture various car types and conditions. | Ensures the model learns from a wide range of car listings, improving its ability to provide accurate valuations for different car models and conditions |
| | Ensure the training data is balanced and covers different price ranges. | Prevents bias towards certain car types or price ranges, leading to more accurate and fair valuations for all types of listings on Gumtree. |
| **Testing and Evaluation** | Validate the model using a separate validation set to tune hyperparameters and avoid overfitting. | Helps in fine-tuning the model to achieve optimal performance specific to Gumtree's car listings by adjusting hyperparameters without overfitting. |
| | Test the final model on an unseen test set to assess its generalisation ability. | Provides an unbiased evaluation of the model's performance on new, unseen car listings, ensuring reliable and accurate valuations for Gumtree users. |
| **Evaluation Metrics** | MAE: Measures the average magnitude of errors in predictions. | Indicates how close the predicted car prices are to the actual listings, with lower MAE indicating better valuation accuracy for Gumtree users. |
| | RMSE: Measures the square root of the average squared differences between predicted and actual values. | Penalizes larger errors more than MAE, providing a more sensitive metric for evaluating the model's performance on Gumtree car listings. |
| | R-squared: Indicates the proportion of the variance in the dependent variable that is predictable from the independent variables. | Reflects the model's ability to explain the variance in car prices, with higher R-squared values indicating better explanatory power, crucial for Gumtree. |
| **Data Partition** | Training Set: 70% of the data, used to train the model. | Ensures the model learns patterns, relationships, and structure from a substantial portion of Gumtree's car listings data. |
| | Validation Set: 15% of the data, used to tune the model. | Allows for the adjustment of model configuration and tuning of hyperparameters to optimize performance on Gumtree's platform. |
| | Testing Set: 15% of the data, used to evaluate the final model. | Provides an unbiased evaluation of the model's performance and assesses how well the model generalizes to new car listings on Gumtree. |
| | Data Partition 70-15-15 Split: Balanced datasets for training, with sufficient data for validation and testing. | Ensures a good balance, allowing for sufficient training while leaving enough data for validation and testing to avoid overfitting and ensure reliable metrics for Gumtree. |

### MODEL TRAINING PROCESS

The training process involves a detailed selection and preparation of features such as brand, model, mileage, age, location, engine size, transmission type, body type, seller type, days listed, and seller reputation. These features are crucial for accurate car valuations. The data is split into training (70%), validation (15%), and testing (15%) sets. This split ensures a robust training process, ample data for hyperparameter tuning, and a reliable assessment of model performance. Hyperparameter tuning, performed on the validation set, is critical for optimising model accuracy and preventing overfitting, ensuring the model adapts well to new data. According to research, hyperparameter tuning significantly impacts model performance and accuracy, making it an essential step in the machine learning process (Schratz et al., 2019).

### MODEL TESTING AND EVALUATION

The testing process involves evaluating the final model on the test set, providing an unbiased measure of its performance on unseen data. This is crucial for understanding how well the model generalizes to new listings. Key metrics used for evaluation include MAE, RMSE, and R-squared, which provide insights into the model's accuracy and predictive power. Comparing these metrics across different models, such as regression, decision trees, and random forests, ensures the selection of the best-performing algorithm. This comprehensive evaluation process is like Amazon's approach, where continuous model refinement and rigorous testing underpin their recommendation systems' success.

### STEP 5: DEPLOYMENT AND INTEGRATION
• Integrate the model into the car listing workflow for real-time pricing suggestions.

### STEP 6: CONTINUOUS MONITORING AND IMPROVEMENT
• Monitor model performance, collect user feedback, and update the model regularly to adapt to changing market conditions and user behaviours.

---

## ARTICULATE THE EXPECTED OUTCOMES OF THE ANALYSIS AND DISCUSS HOW THIS WOULD ADDRESS THE BUSINESS PROBLEM

The implementation of AI/ML techniques to enhance Gumtree's car valuation process is anticipated to yield several significant outcomes, effectively addressing the core business problem of inaccurate car valuations and improving user satisfaction and transaction success rates.

### TABLE 9: EXPECTED OUTCOMES

| Expected Outcomes | How it Addresses Gumtree's Business Problem |
|------------------|---------------------------------------------|
| Enhanced Pricing Accuracy | Ensures precise car valuations, reducing errors and user dissatisfaction. |
| Improved User Experience | Streamlines the listing process, increasing platform activity and loyalty. |
| Continuous Adaptation to Market Trends | Keeps valuations up to date, maintaining relevance and competitiveness. |
| Building Trust and Enhancing Credibility | Provides reliable pricing, increasing buyer and seller confidence. |
| Increased Operational Efficiency | Reduces manual errors, allowing resource reallocation to innovation. |
| Actionable Insights and Strategic Decision Making | Informs strategic decisions, guiding marketing and user engagement efforts. |

### ENHANCED PRICING ACCURACY

By applying machine learning techniques such as regression analysis, decision trees, and random forests, Gumtree can achieve highly accurate car valuations. These models analyse historical sales data, market trends, and car attributes, ensuring that the pricing is precise. Accurate pricing will resolve the issue of cars being overpriced or underpriced, which currently leads to reduced transaction success (Jimenez, 2024). This improvement will result in quicker sales and increased transaction volumes as sellers will benefit from fair pricing, while buyers will avoid the risk of overpaying, enhancing overall satisfaction with the platform.

### IMPROVED USER EXPERIENCE

Automating the car valuation process will streamline the listing process for sellers, making it easier and faster to list vehicles. Immediate, data-driven price suggestions will reduce the effort required to create a listing. This enhanced user experience is expected to attract more users to the platform, increasing the number of active listings and transactions (Mckinsey, 2023). A user-friendly listing process will also encourage repeat use and foster loyalty to Gumtree. As a result, platform activity will rise, leading to higher revenue and market share.

### CONTINUOUS ADAPTATION TO MARKET TRENDS

Machine learning models, particularly neural networks, have the capability to learn from new data continuously (Khandelwal, 2023). This feature allows Gumtree to keep their car valuation models updated with the latest market trends and user behaviours. By continuously updating the models, Gumtree can quickly adapt to market fluctuations, ensuring that car valuations remain accurate and relevant despite economic changes. This continuous improvement will help Gumtree maintain a competitive edge, as users will always receive the most accurate and current pricing information.

### BUILDING TRUST AND ENHANCING CREDIBILITY

Accurate and reliable car valuations will enhance Gumtree's reputation as a trustworthy and credible marketplace. Buyers will have more confidence in the listed prices, reducing the risk of overpaying, while sellers will be able to price their cars competitively, increasing the likelihood of quick sales (Infosys, 2024). This trust and reliability are also crucial for user retention and satisfaction. As Gumtree becomes known for providing precise and fair valuations, more users will likely choose it over competitors, further boosting the platform's user base and transaction volumes.

### INCREASED OPERATIONAL EFFICIENCY

Implementing AI-driven analytics will enhance Gumtree's operational efficiency. The automated valuation process will minimize the reliance on manual pricing, reducing human errors and inconsistencies (Zartis, 2024). This efficiency gain will allow Gumtree to allocate resources more effectively, focusing on other areas of improvement and innovation. Moreover, the use of advanced ML techniques will enable quicker updates and refinements to the valuation models, ensuring that the platform remains responsive to market changes and user needs.

---

## CONCLUSION

This report highlights the potential of AI-driven analytics in addressing Gumtree's challenges in car sales. By automating the car valuation process using advanced machine learning techniques like regression analysis, decision trees, and random forests, Gumtree can significantly enhance pricing accuracy, streamline the listing process, and continuously adapt to market trends. This approach will build trust, improve user satisfaction, and increase transaction success rates. Implementing these AI solutions will also boost operational efficiency by reducing manual errors and allowing for more effective resource allocation, thereby positioning Gumtree more competitively in the online car sales market.

---

## REFERENCES

Albert, B., et al. (2013). Measuring the user experience: collecting, analyzing, and presenting usability metrics. Newnes.

Ali, J., et al. (2012). Random forests and decision trees. International Journal of Computer Science Issues (IJCSI), 9(5), p.272.

Alqahtani, A.Y. and Rajkhan, A.A. (2020). E-Learning Critical Success Factors during the COVID-19 Pandemic: A Comprehensive Analysis of E-Learning Managerial Perspectives. Education Sciences, 10(9), p.216. viewed 17 July 2024, https://doi.org/10.3390/educsci10090216.

Alquthami, T., Zulfiqar, M., Kamran, M., Milyani, A.H. and Rasheed, M.B. (2022). A performance comparison of machine learning algorithms for load forecasting in smart grid. IEEE Access, 10, pp.48419-48433.

Asadi, M., Roudari, S., Tiwari, A.K. and Roubaud, D. (2023). Scrutinizing commodity markets by quantile spillovers: A case study of the Australian economy. Energy Economics, 118, p.106482. viewed 8 July 2024, https://doi.org/10.1016/j.eneco.2022.106482.

Australian Bureau Of Statistics. (2023). Australian Bureau of Statistics, Australian Government. viewed 8 July 2024, Abs.gov.au, https://www.abs.gov.au/.

Australian Government. (2024). Australian Government Initiatives to Reduce Emissions from Road Transport. viewed 8 July 2024, Department of Infrastructure, Transport, Regional Development, Communications and the Arts, https://www.infrastructure.gov.au/infrastructure-transportvehicles/vehicles/vehicle-safety-environment/australian-government-initiativesreduce-emissions-road-transport.

Borah, A. and Rutz, O. (2024). Enhanced sales forecasting model using textual search data: Fusing dynamics with big data. International Journal of Research in Marketing. viewed 10 July 2024, https://doi.org/10.1016/j.ijresmar.2024.05.007.

carsales.com.au. (2024). Free Car Valuations - Value My Car - carsales.com.au. viewed 10 July 2024, https://www.carsales.com.au/car-valuations/.

Çetin, V., et al. (2022). A comprehensive review on data preprocessing techniques in data analysis. Pamukkale Üniversitesi Mühendislik Bilimleri Dergisi, 28(2), pp.299-312.

CMOE. (2024). Critical Success Factors. viewed 8 July 2024, https://cmoe.com/glossary/critical-successfactors/#:~:text=Critical%20success%20factors%20should%20be.

Cong, Z., Luo, X., Pei, J., Zhu, F. and Zhang, Y. (2022). Data pricing in machine learning pipelines. Knowledge and Information Systems, 64(6), pp.1417-1455.

Commission, A.C. and C. (2023). Buying online. viewed 8 July 2024, www.accc.gov.au, https://www.accc.gov.au/consumers/buying-products-andservices/buying-online#:~:text=When%20a%20consumer%20shops%20online.

CWRU Online Engineering. (2024). Advancements in Artificial Intelligence and Machine Learning. viewed 10 July 2024, https://onlineengineering.case.edu/blog/advancements-in-artificial-intelligence-and-machinelearning#:~:text=In%20recent%20years%2C%20advancements%20in.

Erfan, S. and Dhaka, A. (2021). Second Hand Price Prediction for Tesla Vehicles. viewed 17 July 2024, https://arxiv.org/pdf/2101.03788.

Fic, A., et al. (2022). The Influence of Macroeconomic Variables on Sales of Car Manufacturers. FAIMA Business & Management Journal, 10(1).

Grewal, D., et al. (2004). The effects of buyer identification and purchase timing on consumers' perceptions of trust, price fairness, and repurchase intentions. Journal of Interactive Marketing, 18(4), pp.87-100.

Gumtree. (2024). About Us. viewed 17 July 2024, https://www.gumtree.com/info/life/aboutus/#:~:text=Our%20vision%20%26%20mission&text=It.

IBISWorld. (2023). IBISWorld - Industry Market Research, Reports, and Statistics. viewed 8 July 2024, https://www.ibisworld.com/au/industry/automotiveindustry/1913/.

Infosys. (2024). Building brand reputation and trust on online marketplaces | Infosys BPM. viewed 17 July 2024, www.infosysbpm.com, https://www.infosysbpm.com/blogs/digital-business-services/building-brandreputation-and-trust-on-online-marketplaces.html.

Jimenez, E. (2024). Used car price prediction using different machine learning models. viewed 17 July 2024, Medium, https://medium.com/@edwngjs/used-carprice-prediction-using-different-machine-learning-models-e3fb2ee496d6.

Julianelli, V., Caiado, R.G.G., Scavarda, L.F. and Cruz, S.P. de M.F. (2020). Interplay between reverse logistics and circular economy: Critical success factorsbased taxonomy and framework. Resources, Conservation and Recycling, 158, p.104784. viewed 17 July 2024, https://doi.org/10.1016/j.resconrec.2020.104784.

Kashyap, P. (2023). Machine learning algorithms and their relationship with modern technologies. In Machine learning for decision makers: cognitive computing fundamentals for better decision making (pp. 165-245). Berkeley, CA: Apress.

Khandelwal, R. (2023). Unraveling the Power of Neural Networks in Financial Technology. viewed 17 July 2024, Medium, https://medium.com/@rishab.khandelwal20/unraveling-the-power-of-neuralnetworks-in-financial-technology-c6534f241f4f#:~:text=to%20human%20traders.-.

Laurell, C. and Sandström, C. (2021). Social media analytics as an enabler for external search and open foresight—The case of Tesla's autopilot and regulatory scrutiny of autonomous driving. IEEE Transactions on Engineering Management, 69(2), pp.564-571.

McKinsey. (2023). Economic potential of generative AI | McKinsey. viewed 17 July 2024, www.mckinsey.com, https://www.mckinsey.com/capabilities/mckinseydigital/our-insights/the-economic-potential-of-generative-ai-the-next-productivityfrontier.

Muharam, H., Chaniago, H., Endraria, E. and Harun, A.B. (2021). E-service quality, customer trust and satisfaction: market place consumer loyalty analysis. Jurnal Minds: Manajemen Ide dan Inspirasi, 8(2), pp.237-254.

Muralidharan, S., Phiri, K., Sinha, S.K. and Kim, B. (2018). Analysis and prediction of real estate prices: a case of the Boston housing market. Issues in Information Systems, 19(2), pp.109-118.

Murray, M. (2023). Big data and machine learning can usher in a new era of policymaking. viewed 8 July 2024, www.hks.harvard.edu, https://www.hks.harvard.edu/faculty-research/policy-topics/science-technologydata/big-data-and-machine-learning-can-usher-new.

Prajwala, T.R., et al. (2015). A comparative study on decision tree and random forest using R tool. International Journal of Advanced Research in Computer and Communication Engineering, 4(1), pp.196-199.

OliverWyman. (2024). How Car Ownership May Become Relic. viewed 8 July 2024, https://www.oliverwyman.com/our-expertise/insights/2020/jun/automotiveindustry-at-the-crossroads/customers-sales-and-services/how-car-ownershipmay-become-relic.html.

Panel®. (2024). Council Post: 2024 Mobile Apps: 20 Tech Experts Reveal Top Design Trends. viewed 8 July 2024, Forbes, https://www.forbes.com/sites/forbestechcouncil/2024/01/25/2024-mobile-apps-20-tech-experts-reveal-top-design-trends/.

Pattnaik, D., Ray, S. and Raman, R. (2024). Applications of artificial intelligence and machine learning in the financial services industry: A bibliometric review. Heliyon, 10(1), p.e23492. viewed 8 July 2024, https://doi.org/10.1016/j.heliyon.2023.e23492.

Reserve Bank of Australia. (2024). Inflation overview. viewed 8 July 2024, Reserve Bank of Australia, https://www.rba.gov.au/inflation-overview.html.

Rogers, C. (2020). Buying a Car During Coronavirus: Tips & Advice. viewed 10 July 2024, Edmunds, https://www.edmunds.com/car-buying/online-car-shoppingduring-social-distancing.html.

SANDUA, D. (2024). MULTIPLY BUSINESS RESULTS WITH ARTIFICIAL INTELLIGENCE. viewed 17 July 2024, Google Books, Amazon Digital Services LLC - Kdp, https://books.google.com.au/books?hl=en&lr=&id=Wv3UEAAAQBAJ&oi=fnd&pg=PA9&dq=tisfaction.+Incorrect+pricing+affects+sales+and+profits.

Schratz, P., et al. (2019). Hyperparameter tuning and performance assessment of statistical and machine-learning algorithms using spatial data. Ecological Modelling, 406, pp.109–120. viewed 10 July 2024, https://doi.org/10.1016/j.ecolmodel.2019.06.002.

Sonsale, S. and Phadtare, P. (2022). The impact of epidemic outbreaks on the consumer behaviour towards the automobile industry with a special focus on the pre and post COVID-19 era. INDUSTRIAL, MECHANICAL AND ELECTRICAL ENGINEERING, 2519(1). viewed 10 July 2024, https://doi.org/10.1063/5.0110606.

Steck, H., Baltrunas, L., Elahi, E., Liang, D., Raimond, Y. and Basilico, J. (2021). Deep Learning for Recommender Systems: A Netflix Case Study. AI Magazine, 42(3), pp.7–18.

Thompson, N. (2023). State of Electric Vehicles Report 2023 - Electric Vehicle Council. viewed 8 July 2024, https://electricvehiclecouncil.com.au/reports/soevsreport-2023/.

Valdez-Juárez, L.E., et al. (2021). Online Buyers and Open Innovation: Security, Experience, and Satisfaction. Journal of Open Innovation: Technology, Market, and Complexity, 7(1), p.37. viewed 10 July 2024, https://doi.org/10.3390/joitmc7010037.

Zartis. (2024). AI for Operational Efficiency - Use Cases and Examples. viewed 17 July 2024, https://www.zartis.com/ai-for-operational-efficiency-use-cases-andexamples/.

---

## APPENDIX

### PEST ANALYSIS FRAMEWORK

#### PEST Analysis of Gumtree and the Auto industry

| Factor | Description | Impact on Gumtree |
|--------|-------------|-------------------|
| **Political** | Government regulations on online marketplaces and data protection laws (Commission, 2023). | Comply with these laws and regulations |
| | Environmental Regulations which affect vehicle emissions and sales (Australian Government, 2024). | Potential benefits from government schemes/incentives for eco-friendly vehicles |
| **Economic** | Economic conditions such as high cash rates and inflation affecting consumer purchasing power, thus prices (Australian Bureau of Statistics, 2023). | Can impact consumer decisions such as an economic downturn reducing the number of transactions on Gumtree |
| | Fluctuations in fuel prices can impact vehicle sales (Asadi et al., 2023). | Changes in fuel prices may influence the demand for different types of vehicles. |
| | Inflation rates can also impact pricing strategies (Reserve Bank of Australia, 2024). | Gumtree may offer incentives to impact demand of auto vehicles. |
| **Social** | Increasing consumer preference for eco-friendly and electric vehicles (Thompson, 2023). | Need to include more eco-friendly vehicle options and related features (particularly in search results etc). |
| | Growing trust and reliance for online marketplaces (Muharam et al., 2021). | Building and Maintaining user trust through accurate prices and services. |
| | Demographic shifts impacting car ownership trends (e.g., urbanization, younger generation's preference for shared mobility and public transportation) (OliverWyman, 2024). | Adapt to changes in car ownership societal changes. |
| **Technological** | Advancements in AI and machine learning for accurate pricing and fraud detection (Pattnaik et.al, 2024). | Investing in AI-driven analytics to enhance accuracy of prices. |
| | Advancements in mobile technology and Apps (Panel®, 2024). | Enhance app features to improve user experience. |
| | Development of advanced data analytics tools for market insights (Murray, 2023). | Utilise data analytics for market trends and user needs. |

### CHALLENGE 1: INCOMPLETE/MISSING DATA

#### FIGURE A: CHALLENGE 1

[The document shows a data table with various car listings where several fields contain missing data, highlighted to show the challenge of incomplete/missing data in the dataset. The table includes columns for listing details with many "Other" entries and missing values across different attributes like transmission, manufacturer, etc.]