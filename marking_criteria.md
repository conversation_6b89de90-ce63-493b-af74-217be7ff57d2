# INFS3822 – AI for Business Analytics
## Marking Criteria Individual Assignment 2

| Criteria & Weight | Fail (0% - 49%) | Pass (50% - 64%) | Credit (65%-74%) | Distinction (75%-84%) | High Distinction (85% - 100%) |
|---|---|---|---|---|---|
| **Business Problem Statement (20 marks)** | • Incomplete or vague problem statement.<br>• Poor or no application of a design thinking framework.<br>• Lack of clarity in articulating the business objective.<br>• Missing or inadequate discussion of underlying causes and critical factors. | • Basic problem statement provided with minimal detail.<br>• Some attempt to apply a design thinking framework, but lacks depth.<br>• Basic articulation of the business objective.<br>• Partial discussion of underlying causes and critical factors. | • Clear and concise problem statement.<br>• Adequate application of one design thinking framework.<br>• Clearly articulated business objective.<br>• Good discussion of underlying causes and critical factors. | • Well-articulated problem statement with clear context.<br>• Comprehensive application of one design thinking framework.<br>• Well-articulated business objective that aligns with the problem.<br>• Detailed discussion of underlying causes and critical factors. | • Exceptionally clear and detailed problem statement with strong context.<br>• Insightful and thorough application of one design thinking framework.<br>• Thoroughly articulated and relevant business objective.<br>• In-depth discussion and analysis of underlying causes and critical factors. |
| **Key Proposed Solution (30 marks)** | • The key proposed Solution is unclear, irrelevant, or poorly explained.<br>• No application of a design thinking framework.<br>• Weak justification and lack of examples or references.<br>• Poor visualisation to illustrate the methodology undertaken in the project, the proposed solution and the different steps. | • Basic explanation of proposed solutions with some relevance.<br>• Application of one design thinking framework but lacks coherence.<br>• Basic justification with minimal examples or references.<br>• Basic visualisation to illustrate the project methodology, the proposed solution, the different steps; but lacks some details. | • Good explanation of proposed solutions with relevance to the business problem.<br>• Application of one design thinking framework.<br>• Good justification supported by relevant examples and references.<br>• Good visualisation to illustrate the project methodology along with the proposed solution, outlining the different steps- but lack few details. | • Excellent explanation of proposed solutions with strong relevance.<br>• Comprehensive application of one design thinking framework.<br>• Excellent justification, well-supported with relevant examples and academic references.<br>• Excellent and detailed visualisation to illustrate the methodology, the proposed solution, outlining the different steps. | • Exceptional and insightful explanation of proposed solutions.<br>• Thorough application of one design thinking framework.<br>• Outstanding and well-supported justification with numerous relevant examples and academic references.<br>• Outstanding visualisation to illustrate the methodology, the proposed solution, outlining the different steps with great detail. |

## Translate the Business Problem into ML Problems (30 marks)

### Machine Learning Problems

| Criteria & Weight | Fail (0% - 49%) | Pass (50% - 64%) | Credit (65%-74%) | Distinction (75%-84%) | High Distinction (85% - 100%) |
|---|---|---|---|---|---|
| **Machine Learning Problems** | • Does not clearly or correctly convert the business problem into specific ML problems.<br>• Unclear or incorrect rationale behind ML techniques/methods.<br>• Justifications for the chosen ML technique are missing or poorly explained. | • The business problem is somewhat converted into ML problems but lacks depth and precision.<br>• Basic rationale behind the selection of a suitable ML techniques/methods.<br>• The justifications for the chosen ML technique are provided but lack details. | • The business problem is correctly converted into specific ML problems with adequate detail.<br>• Clear rationale behind the selection of a suitable ML technique.<br>• The justifications for the chosen ML technique are clear and adequately explained. | • The business problem is correctly converted into specific ML problems with detailed discussion.<br>• Detailed and well-reasoned rationale behind the selection of suitable ML techniques/methods.<br>• The justifications for the chosen ML technique are comprehensive and well-articulated. | • The business problem is correctly converted into specific ML problems with extensive detail.<br>• Exceptionally well-reasoned and insightful rationale behind the selection of suitable ML techniques/methods.<br>• The justifications for the chosen ML technique are thorough, well-supported, and clearly articulated. |

### Machine Learning Algorithm

| Criteria & Weight | Fail (0% - 49%) | Pass (50% - 64%) | Credit (65%-74%) | Distinction (75%-84%) | High Distinction (85% - 100%) |
|---|---|---|---|---|---|
| **Machine Learning Algorithm** | • Does not clearly or correctly explain the chosen ML algorithm.<br>• The approach and justification for the chosen ML algorithm are missing or incorrect. | • The selected ML algorithm is mentioned but lacks detail.<br>• The approach to determine a suitable algorithm is somewhat explained.<br>• The justification for the chosen ML algorithm is present but lacks depth. | • The selected ML algorithm is clear and adequately detailed.<br>• The approach to determine a suitable algorithm is clearly explained.<br>• The justification for the chosen ML algorithm is adequate. | • The selected ML algorithm is very clear and detailed.<br>• The approach to determine a suitable ML algorithm is well-explained.<br>• The justification for the chosen algorithm is thorough and well-supported. | • The selected ML algorithm is exceptionally clear and detailed.<br>• The approach to determine a suitable ML algorithm is well-explained, and comprehensive.<br>• The justification for the chosen algorithm is thorough, well-supported, and clearly articulated. |

### Preparation of the Data

| Criteria & Weight | Fail (0% - 49%) | Pass (50% - 64%) | Credit (65%-74%) | Distinction (75%-84%) | High Distinction (85% - 100%) |
|---|---|---|---|---|---|
| **Preparation of the Data** | • Does not clearly or correctly explain the data cleaning steps.<br>• Justifications for data cleaning steps are missing or incorrect.<br>• Does not clearly or correctly explain the data transformation.<br>• Justifications for data transformation steps are missing or incorrect.<br>• Minimal discussion of challenges they may encounter during the data analysis. | • The data cleaning steps are discussed but lack detail.<br>• The justifications for data cleaning steps are provided but lack depth.<br>• The data transformation steps are discussed but lack detail.<br>• The justifications for data transformation steps are discussed but lack depth.<br>• Basic discussion of challenges they may encounter during the data analysis. | • The data cleaning steps are clear and adequately discussed.<br>• The justifications for data cleaning steps are adequate.<br>• The data transformation techniques are clear and adequately discussed with some detail.<br>• The justifications for data transformation steps are adequately discussed.<br>• Good discussion of challenges they may encounter during the data analysis. | • The data cleaning steps are very clear and discussed in detail.<br>• The justifications for data cleaning steps are thorough and well-articulated.<br>• The data transformation techniques are very clear and detailed.<br>• The justifications for data transformation steps are well discussed.<br>• Thorough discussion of challenges they may encounter during the data analysis. | • The data cleaning steps are exceptionally discussed with great, detail.<br>• The justifications for data cleaning steps are thorough, and well-articulated.<br>• The data transformation techniques are exceptionally discussed, with great detail.<br>• The justifications for data transformation steps are thoroughly articulated.<br>• In-depth discussion of challenges they may encounter during the data analysis. |

### Feature Selection

| Criteria & Weight | Fail (0% - 49%) | Pass (50% - 64%) | Credit (65%-74%) | Distinction (75%-84%) | High Distinction (85% - 100%) |
|---|---|---|---|---|---|
| **Feature Selection** | • Does not correctly explain the approach (method) they may apply to determine the key predictors and the target variable for feature selection.<br>• No specific variables were identified or discussed.<br>• Justifications for feature selection are missing or incorrect. | • The approach (method) to determine the key predictors and the target variable is somewhat explained but lacks depth and detail.<br>• Key predictors and target variables are mentioned but lacks clarity and justification. | • The approach (method) to determine the key predictors and the target variable to be used for prediction is discussed with adequate detail.<br>• Key predictors and target variables are discussed and justified. | • The approach (method) to determine the key predictors and the target variable for prediction is explained in a detailed manner.<br>• Key predictors and target variables are discussed and comprehensively justified. | • The approach (method) to determine the key predictors and the target variable to be used in the model, is explained and exceptionally detailed.<br>• Justifications are sound and convincing.<br>• Key predictors and target variables are thoroughly discussed with high precision and well justified. |

### Model Training Requirements

| Criteria & Weight | Fail (0% - 49%) | Pass (50% - 64%) | Credit (65%-74%) | Distinction (75%-84%) | High Distinction (85% - 100%) |
|---|---|---|---|---|---|
| **Model Training requirements** | • Incomplete or missing explanation of the training process.<br>• No discussion of how to split the training and testing data<br>• No justification for the choice of training and testing data split.<br>• Lack of discussion of hyperparameter tuning. | • Basic explanation of the training process with limited detail.<br>• Basic discussion of how to split the training and testing data<br>• Minimal justification for the training and testing data split.<br>• Brief discussion of hyperparameter tuning with little detail. | • Clear explanation of the training process using selected features.<br>• Good discussion of how to split the training and testing data<br>• Adequate justification for the training and testing data split.<br>• Discussion of hyperparameter tuning results to enhance the model's performance. | • Detailed explanation of the training process using selected features.<br>• Excellent discussion of how to split the training and testing data<br>• Well-justified choice of training and testing data split.<br>• Comprehensive discussion of hyperparameter tuning with clear impact on the model's performance. | • Thorough and insightful explanation of the training process with selected features.<br>• Outstanding discussion of how to split the training and testing data<br>• Strong justification for the training and testing data split.<br>• In-depth discussion of hyperparameter tuning, demonstrating its importance to significantly enhance the model performance. |

### Model Testing and Evaluation Requirements

| Criteria & Weight | Fail (0% - 49%) | Pass (50% - 64%) | Credit (65%-74%) | Distinction (75%-84%) | High Distinction (85% - 100%) |
|---|---|---|---|---|---|
| **Model Testing and Evaluation requirements** | • Incomplete or missing discussion of model testing process.<br>• No discussion of the importance of model evaluation and comparison between models. | • Basic discussion of model testing process.<br>• Basic discussion of the importance of model evaluation with limited detail.<br>• Basic discussion of the need to compare the predictive performance between models. | • Clear discussion of model testing process.<br>• Good discussion of the importance of model evaluation.<br>• Good discussion of the need to compare the predictive performance between models. | • Detailed discussion of model testing process.<br>• Excellent discussion of the importance of model evaluation.<br>• Excellent discussion of the need to compare the predictive performance between models. | • Comprehensive discussion of model testing process.<br>• Outstanding discussion of the importance of model evaluation.<br>• Outstanding discussion of the need to compare the predictive performance between models. |

## Additional Criteria

| Criteria & Weight | Fail (0% - 49%) | Pass (50% - 64%) | Credit (65%-74%) | Distinction (75%-84%) | High Distinction (85% - 100%) |
|---|---|---|---|---|---|
| **The Expected Outcomes and benefits (20 marks)** | • Vague or unclear discussion of expected outcomes.<br>• Minimal or no explanation of how the analysis' outcomes address the business problem. | • Basic discussion of expected outcomes.<br>• Some explanation of how the analysis' outcomes address the business problem. | • Good discussion of expected outcomes.<br>• Good explanation of how analysis' outcomes address the business problem. | • Excellent discussion of expected outcomes.<br>• Comprehensive explanation of how the analysis' outcomes address the business problem. | • Exceptionally detailed and insightful discussion of expected outcomes.<br>• Thorough explanation of how the outcomes of your analysis address the business problem, demonstrating clear benefits to the company. |
| **Writing and Structure of Report** | • Your writing is not professional in tone and has major spelling and grammatical errors.<br>• Your written expression does not indicate a logic/flow between each section of the report.<br>• Poor or unclear structure.<br>• Your sources have not been referenced, and/or there are excessive errors in referencing in the report.<br>• The word limit has not been adhered to. | • Some attempt has been made to use a professional tone and presentation in your writing, but there are some spelling and grammatical errors.<br>• You have endeavoured to provide logic/flow between each section of the report.<br>• Attempt to a good structure but lack coherent flow between sections.<br>• Some sources are referenced throughout the report, but there are errors in your referencing of sources. | • Your writing is mostly professional in tone and presentation, but occasional spelling and/or grammatical errors exist.<br>• Your written expression indicates the logic/flow between each section of the report.<br>• Good structure with organised headings.<br>• Most sources are referenced throughout the report, with only minor errors in referencing. | • Your writing is professional in tone and presentation, with a few very minor spellings and/or grammatical errors.<br>• Your written expression strongly indicates the logic/flow between each section of the report.<br>• Good structure with organised headings and coherent follow between sections.<br>• All sources are referenced throughout the report with only minor errors in referencing. | • Your writing is professional in tone and presented outstandingly with no spelling or grammatical errors.<br>• Your written expression provides a strong and coherent indication of the logic/flow between each section, enabling key arguments to develop fully.<br>• Good structure with organised headings and coherent follow between sections.<br>• All sources are referenced throughout the report, and the sources are used very well, with no significant errors in referencing. |